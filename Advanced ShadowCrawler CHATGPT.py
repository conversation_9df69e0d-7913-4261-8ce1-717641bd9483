import requests
import threading
import time
import random
import string
import json
import hashlib
import base64
import os
import sys
import io
import sounddevice as sd
import soundfile as sf
import pyautogui
import cv2
import numpy as np
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import tkinter as tk
from tkinter import simpledialog, messagebox

# Phase 6: Full integration with Ghost AI, GUI, surveillance

class CryptoModule:
    def __init__(self, key_phrase="ShadowReaperKey"):
        salt = b'shadow_salt'
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        self.key = kdf.derive(key_phrase.encode())

    def encrypt_aes(self, data):
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(self.key), modes.CFB(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        ct = encryptor.update(data.encode()) + encryptor.finalize()
        return base64.b64encode(iv + ct).decode()

    def decrypt_aes(self, token):
        raw = base64.b64decode(token.encode())
        iv = raw[:16]
        ct = raw[16:]
        cipher = Cipher(algorithms.AES(self.key), modes.CFB(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        return (decryptor.update(ct) + decryptor.finalize()).decode()

class GhostAI:
    def __init__(self):
        self.targets = ["http://example.com", "http://test.com"]
        self.behavior_patterns = ["click", "scroll", "form-fill", "idle", "navigate"]

    def select_target(self):
        return random.choice(self.targets)

    def analyze_behavior(self):
        return random.choice(self.behavior_patterns)

    def simulate_decision(self):
        action = random.choice(["collect", "scan", "rotate_proxy"])
        print(f"[GhostAI] Decision made: {action}")
        return action

class RealC2Communicator:
    def __init__(self, endpoint, crypto: CryptoModule):
        self.endpoint = endpoint
        self.crypto = crypto
        self.command_queue = ["collect", "scan", "rotate_proxy"]
        self.command_index = 0

    def send_encrypted_data(self, data):
        payload = {
            "data": self.crypto.encrypt_aes(json.dumps(data))
        }
        print(f"[+] Data exfiltration simulation: {json.dumps(data)[:50]}...")

    def fetch_commands(self):
        if random.random() < 0.3:
            command = self.command_queue[self.command_index]
            self.command_index = (self.command_index + 1) % len(self.command_queue)
            print(f"[+] Received command from simulated C2: {command}")
            return command
        return None

class ShadowCrawler:
    def __init__(self):
        self.crypto = CryptoModule()
        self.ai_model = GhostAI()
        self.c2 = RealC2Communicator("https://simulation.local/c2", self.crypto)
        self.proxy_pool = ["http://************:8080", "http://***********:3128", "socks5h://127.0.0.1:9050"]
        self.current_proxy = random.choice(self.proxy_pool)
        self.tor_enabled = self.init_tor()
        self.validate_integrity()

    def validate_integrity(self):
        print("[+] Code integrity verified.")

    def init_tor(self):
        print("[+] Tor simulation mode enabled.")
        return True

    def test_proxy(self, proxy):
        print(f"[+] Simulated testing of proxy: {proxy}")
        return "socks5h" in proxy or random.choice([True, False])

    def rotate_proxy(self):
        valid = [p for p in self.proxy_pool if self.test_proxy(p)]
        if valid:
            self.current_proxy = random.choice(valid)
            print(f"Proxy switched: {self.current_proxy}")
        else:
            print("[!] No valid proxy available")

    def adaptive_scanning(self, target):
        print(f"[+] Simulated scanning of {target}")
        return f"<html><body>Scan result of {target}</body></html>"

    def collect_data(self):
        return {
            "sys": {
                "platform": sys.platform,
                "cpu_cores": os.cpu_count(),
                "memory_gb": round(random.uniform(4.0, 32.0), 2)
            },
            "net": {
                "ip": f"192.168.{random.randint(0, 255)}.{random.randint(1, 254)}"
            },
            "behavior": self.ai_model.analyze_behavior()
        }

    def exfiltrate(self, data):
        self.c2.send_encrypted_data(data)

    def self_destruct(self):
        print("[!!!] Self-Destruct Simulation Activated 💣")
        print("[+] Exiting program safely...")
        sys.exit()

    def record_audio(self, filename="audio.wav", duration=3):
        samplerate = 44100
        data = sd.rec(int(duration * samplerate), samplerate=samplerate, channels=2)
        sd.wait()
        sf.write(filename, data, samplerate)
        print(f"[+] Audio recorded: {filename}")

    def capture_screen(self, filename="screenshot.png"):
        screenshot = pyautogui.screenshot()
        screenshot.save(filename)
        print(f"[+] Screenshot captured: {filename}")

    def capture_webcam(self, filename="webcam.jpg"):
        cap = cv2.VideoCapture(0)
        ret, frame = cap.read()
        if ret:
            cv2.imwrite(filename, frame)
            print(f"[+] Webcam image saved: {filename}")
        cap.release()

class GUI:
    def __init__(self, crawler: ShadowCrawler):
        self.crawler = crawler
        self.root = tk.Tk()

        # Set dark theme colors
        self.bg_color = "#1e1e1e"  # Dark background
        self.fg_color = "#00ff00"  # Matrix green text
        self.button_bg = "#2d2d2d"  # Slightly lighter background for buttons
        self.button_fg = "#00ff00"  # Green text for buttons
        self.highlight_bg = "#3a3a3a"  # Highlight color
        self.title_color = "#00ffff"  # Cyan for titles

        # Create a StringVar to capture console output
        self.console_output = None

        # Redirect stdout to capture print statements
        self.old_stdout = sys.stdout
        sys.stdout = self

        # Skip authentication for now and show interface directly
        self.show_interface()

    def write(self, text):
        # This method is called when print() is used
        self.old_stdout.write(text)  # Still write to terminal
        if self.console_output and hasattr(self, 'console_text'):
            self.console_text.config(state=tk.NORMAL)
            self.console_text.insert(tk.END, text)
            self.console_text.see(tk.END)
            self.console_text.config(state=tk.DISABLED)

    def flush(self):
        # Required for file-like objects
        self.old_stdout.flush()

    def show_interface(self):
        self.root.deiconify()
        self.root.title("Advanced ShadowCrawler - Command Center")
        self.root.geometry("800x600")  # Larger window
        self.root.configure(bg=self.bg_color)
        self.root.resizable(True, True)

        # Center the window
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Title frame
        title_frame = tk.Frame(self.root, bg=self.bg_color)
        title_frame.pack(fill=tk.X, padx=20, pady=20)

        title_label = tk.Label(title_frame, text="🕸️ ADVANCED SHADOW CRAWLER 🕸️",
                              font=("Consolas", 18, "bold"), bg=self.bg_color, fg=self.title_color)
        title_label.pack()

        subtitle_label = tk.Label(title_frame, text="GHOST AI COMMAND CENTER",
                                 font=("Consolas", 12), bg=self.bg_color, fg=self.fg_color)
        subtitle_label.pack(pady=5)

        # Status frame
        status_frame = tk.Frame(self.root, bg=self.button_bg, padx=10, pady=10)
        status_frame.pack(fill=tk.X, padx=20, pady=10)

        self.status_label = tk.Label(status_frame, text="🧠 Ghost AI Status: Active",
                                   font=("Consolas", 10), bg=self.button_bg, fg=self.fg_color)
        self.status_label.pack(anchor=tk.W)

        self.proxy_label = tk.Label(status_frame, text=f"🌐 Current Proxy: {self.crawler.current_proxy}",
                                  font=("Consolas", 10), bg=self.button_bg, fg=self.fg_color)
        self.proxy_label.pack(anchor=tk.W)

        # Console output frame
        console_frame = tk.Frame(self.root, bg=self.bg_color)
        console_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        console_label = tk.Label(console_frame, text="📟 CONSOLE OUTPUT",
                               font=("Consolas", 10, "bold"), bg=self.bg_color, fg=self.fg_color)
        console_label.pack(anchor=tk.W, pady=5)

        # Create scrollable text area for console output
        self.console_text = tk.Text(console_frame, height=10,
                                  bg="#000000", fg=self.fg_color,
                                  font=("Consolas", 9),
                                  insertbackground=self.fg_color)
        self.console_text.pack(fill=tk.BOTH, expand=True)
        self.console_text.config(state=tk.DISABLED)

        # Add scrollbar
        scrollbar = tk.Scrollbar(self.console_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.console_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.console_text.yview)

        # Welcome message
        self.console_output = True
        self.write("[+] Advanced ShadowCrawler Console Initialized\n")
        self.write("[+] Ready for operations...\n")

        # Button style
        button_style = {
            "bg": self.button_bg,
            "fg": self.fg_color,
            "activebackground": self.highlight_bg,
            "activeforeground": self.fg_color,
            "font": ("Consolas", 10, "bold"),
            "width": 20,
            "height": 2,
            "relief": tk.FLAT,
            "bd": 0
        }

        # Button frame
        button_frame = tk.Frame(self.root, bg=self.bg_color)
        button_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Create two columns for buttons
        left_frame = tk.Frame(button_frame, bg=self.bg_color)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10)

        right_frame = tk.Frame(button_frame, bg=self.bg_color)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10)

        # Left column buttons
        tk.Button(left_frame, text="▶️ Collect Data", command=self.collect_data, **button_style).pack(pady=10, fill=tk.X)
        tk.Button(left_frame, text="🔍 Scan Target", command=self.scan_target, **button_style).pack(pady=10, fill=tk.X)
        tk.Button(left_frame, text="🔁 Rotate Proxy", command=self.rotate_proxy, **button_style).pack(pady=10, fill=tk.X)

        # Right column buttons
        tk.Button(right_frame, text="🎤 Record Audio", command=self.record_audio, **button_style).pack(pady=10, fill=tk.X)
        tk.Button(right_frame, text="📷 Screenshot", command=self.screenshot, **button_style).pack(pady=10, fill=tk.X)
        tk.Button(right_frame, text="📹 Capture Webcam", command=self.capture_webcam, **button_style).pack(pady=10, fill=tk.X)

        # Footer frame
        footer_frame = tk.Frame(self.root, bg=self.bg_color)
        footer_frame.pack(fill=tk.X, padx=20, pady=20)

        # Self-destruct and exit buttons
        exit_button = tk.Button(footer_frame, text="❌ Exit", command=self.root.quit,
                              bg="#3a3a3a", fg=self.fg_color, font=("Consolas", 10),
                              activebackground="#4a4a4a", activeforeground=self.fg_color,
                              width=15, height=1, relief=tk.FLAT)
        exit_button.pack(side=tk.RIGHT, padx=5)

        destruct_button = tk.Button(footer_frame, text="💣 Self-Destruct", command=self.self_destruct,
                                  bg="#8B0000", fg="#FFFFFF", font=("Consolas", 10, "bold"),
                                  activebackground="#A00000", activeforeground="#FFFFFF",
                                  width=15, height=1, relief=tk.FLAT)
        destruct_button.pack(side=tk.RIGHT, padx=5)

        # Start the main loop
        self.root.mainloop()

    def self_destruct(self):
        self.crawler.self_destruct()

    def collect_data(self):
        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Collecting Data")
        self.write("\n[+] Collecting system information...\n")

        # Get data
        data = self.crawler.collect_data()

        # Format and display data
        formatted_data = json.dumps(data, indent=2)
        self.write(f"[+] Data collected:\n{formatted_data}\n")

        # Exfiltrate data
        self.crawler.exfiltrate(data)

        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Ready")

    def scan_target(self):
        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Scanning Target")

        # Get target and scan
        target = self.crawler.ai_model.select_target()
        self.write(f"\n[+] Selected target: {target}\n")
        self.write(f"[+] Scanning target...\n")

        result = self.crawler.adaptive_scanning(target)

        # Display results
        self.write(f"[+] Scan results:\n{result[:100]}...\n")

        # Exfiltrate data
        self.crawler.exfiltrate({"target": target, "result": result})

        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Ready")

    def rotate_proxy(self):
        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Rotating Proxy")
        self.write("\n[+] Rotating proxy...\n")

        # Rotate proxy
        self.crawler.rotate_proxy()

        # Update proxy label
        self.proxy_label.config(text=f"🌐 Current Proxy: {self.crawler.current_proxy}")

        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Ready")

    def record_audio(self):
        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Recording Audio")
        self.write("\n[+] Recording audio (3 seconds)...\n")

        try:
            # Record audio
            self.crawler.record_audio()
            self.write("[+] Audio recording completed\n")
        except Exception as e:
            self.write(f"[!] Error recording audio: {e}\n")

        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Ready")

    def screenshot(self):
        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Capturing Screenshot")
        self.write("\n[+] Capturing screenshot...\n")

        try:
            # Capture screenshot
            self.crawler.capture_screen()
            self.write("[+] Screenshot captured successfully\n")
        except Exception as e:
            self.write(f"[!] Error capturing screenshot: {e}\n")

        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Ready")

    def capture_webcam(self):
        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Accessing Webcam")
        self.write("\n[+] Accessing webcam...\n")

        try:
            # Capture webcam image
            self.crawler.capture_webcam()
            self.write("[+] Webcam image captured successfully\n")
        except Exception as e:
            self.write(f"[!] Error accessing webcam: {e}\n")

        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: Ready")

    def self_destruct(self):
        # Update status
        self.status_label.config(text="🧠 Ghost AI Status: SELF-DESTRUCT")
        self.write("\n🚨 !!! SELF-DESTRUCT SEQUENCE INITIATED !!! 🚨\n")
        self.write("[+] In a real scenario, the program would delete itself.\n")
        self.write("[+] Exiting program safely...\n")

        # Wait a moment before exiting
        self.root.after(3000, self.crawler.self_destruct)

if __name__ == "__main__":
    print("[+] Starting ShadowCrawler Phase 6 with GhostAI...")
    crawler = ShadowCrawler()
    gui = GUI(crawler)
