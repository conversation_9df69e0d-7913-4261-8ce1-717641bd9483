import tkinter as tk
from tkinter import scrolledtext, ttk
import threading
import time
import random
import json
import hashlib
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os
import sys
import queue

# Advanced ShadowCrawler with GUI – Phase 5: C2 Command Processing

class CryptoModule:
    def __init__(self, key_phrase="ShadowReaperKey"):
        salt = b'shadow_salt'
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        self.key = kdf.derive(key_phrase.encode())

    def encrypt_aes(self, data):
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(self.key), modes.CFB(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        ct = encryptor.update(data.encode()) + encryptor.finalize()
        return base64.b64encode(iv + ct).decode()

    def decrypt_aes(self, token):
        raw = base64.b64decode(token.encode())
        iv = raw[:16]
        ct = raw[16:]
        cipher = Cipher(algorithms.AES(self.key), modes.CFB(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        return (decryptor.update(ct) + decryptor.finalize()).decode()

class RealC2Communicator:
    def __init__(self, endpoint, crypto, log_queue):
        self.endpoint = endpoint
        self.crypto = crypto
        self.command_queue = ["collect", "scan", "rotate_proxy"]
        self.command_index = 0
        self.log_queue = log_queue

    def send_encrypted_data(self, data):
        payload = {
            "data": self.crypto.encrypt_aes(json.dumps(data))
        }
        log_msg = f"[+] Data exfiltration simulation: {json.dumps(data)[:50]}..."
        self.log_queue.put(log_msg)
        
    def fetch_commands(self):
        # Simulate C2 communication by cycling through predefined commands
        if random.random() < 0.3:  # 30% chance to get a command
            command = self.command_queue[self.command_index]
            self.command_index = (self.command_index + 1) % len(self.command_queue)
            self.log_queue.put(f"[+] Received command from simulated C2: {command}")
            return command
        return None

class CommandProcessor:
    def __init__(self, crawler):
        self.crawler = crawler

    def process(self, command):
        self.crawler.log_queue.put(f"[C2] Received command: {command}")
        if command == "collect":
            data = self.crawler.collect_data()
            self.crawler.exfiltrate(data)
        elif command == "scan":
            target = self.crawler.ai_model.select_target()
            result = self.crawler.adaptive_scanning(target)
            self.crawler.exfiltrate({"target": target, "result": result})
        elif command == "rotate_proxy":
            self.crawler.rotate_proxy()
        elif command == "shutdown":
            self.crawler.log_queue.put("[C2] Shutdown triggered.")
            self.crawler.self_destruct()
        else:
            self.crawler.log_queue.put("[C2] Unknown command.")

class ShadowCrawler:
    def __init__(self, profile='AdvancedStealth', log_queue=None):
        self.profile = profile
        self.log_queue = log_queue
        self.proxy_pool = self.generate_proxy_pool()
        self.current_proxy = random.choice(self.proxy_pool)
        self.encryption_key = rsa.generate_private_key(public_exponent=65537, key_size=2048, backend=default_backend())
        self.public_key = self.encryption_key.public_key()
        self.decryption_key = self.encryption_key
        self.crypto = CryptoModule()
        self.c2 = RealC2Communicator("https://simulation.local/c2", self.crypto, log_queue)
        self.command_processor = CommandProcessor(self)
        self.ai_model = AIModel()
        self.tor_enabled = self.init_tor()
        self.validate_integrity()
        self.running = True

    def validate_integrity(self):
        try:
            with open(__file__, 'rb') as f:
                content = f.read()
            current_hash = hashlib.sha256(content).hexdigest()
            known_good = current_hash
            if current_hash != known_good:
                self.log_queue.put("[!] Code integrity check failed.")
                self.self_destruct()
            else:
                self.log_queue.put("[+] Code integrity verified.")
        except Exception as e:
            self.log_queue.put(f"Integrity check error: {e}")

    def init_tor(self):
        # Simplified version without actual Tor connection
        self.log_queue.put("[+] Tor simulation mode enabled.")
        return True

    def generate_proxy_pool(self):
        return [
            "http://123.45.67.89:8080",
            "http://98.76.54.32:3128",
            "socks5h://127.0.0.1:9050"
        ]

    def test_proxy(self, proxy):
        # Simulated proxy testing to avoid actual network requests
        self.log_queue.put(f"[+] Simulated testing of proxy: {proxy}")
        # Return True for the Tor proxy, random for others
        if "socks5h://127.0.0.1:9050" in proxy:
            return True
        return random.choice([True, False])

    def rotate_proxy(self):
        good = [p for p in self.proxy_pool if self.test_proxy(p)]
        if good:
            self.current_proxy = random.choice(good)
            self.log_queue.put(f"Proxy switched: {self.current_proxy}")
        else:
            self.log_queue.put("[!] No valid proxy available")

    def adaptive_scanning(self, target):
        # Simulated scanning to avoid actual network requests
        self.log_queue.put(f"[+] Simulated scanning of {target}")
        scan_results = [
            "<html><head><title>Example Website</title></head><body><h1>Example</h1><p>This is a simulated scan result.</p></body></html>",
            "<html><head><title>Test Site</title></head><body><h1>Test</h1><p>Another simulated scan result.</p></body></html>",
            "<!DOCTYPE html><html><head><meta charset='utf-8'><title>Simulated Page</title></head><body><div>Content</div></body></html>"
        ]
        return random.choice(scan_results)

    def collect_data(self):
        return {
            "sys": self.get_system_info(),
            "net": self.get_network_info(),
            "behavior": self.ai_model.analyze_behavior()
        }

    def get_system_info(self):
        return {
            "platform": sys.platform,
            "cpu_cores": os.cpu_count(),
            "memory_gb": random.uniform(4.0, 32.0)  # Simulated memory value
        }

    def get_network_info(self):
        # Simulated network info to avoid external API calls
        return {
            "ip": {"ip": "192.168." + str(random.randint(0, 255)) + "." + str(random.randint(1, 254))}
        }

    def exfiltrate(self, data):
        self.c2.send_encrypted_data(data)

    def self_destruct(self):
        self.log_queue.put("[!!!] Self-Destruct Simulation Activated 💣")
        self.log_queue.put("[+] In a real scenario, the program would delete itself.")
        self.log_queue.put("[+] Exiting program safely...")
        self.running = False

    def run(self, max_commands=10):
        command_count = 0
        self.log_queue.put("[+] Starting ShadowCrawler in simulation mode...")
        
        while self.running and command_count < max_commands:
            cmd = self.c2.fetch_commands()
            if cmd:
                self.command_processor.process(cmd)
                command_count += 1
                self.log_queue.put(f"[+] Processed {command_count}/{max_commands} commands")
            time.sleep(2)  # Shorter sleep time
            
        self.log_queue.put("[+] Simulation completed successfully!")
        self.log_queue.put("[+] Exiting program...")
        return True

class AIModel:
    def __init__(self):
        self.targets = ["http://example.com", "http://test.com"]
        self.behavior_patterns = ["click", "scroll", "form-fill"]

    def select_target(self):
        return random.choice(self.targets)

    def analyze_behavior(self):
        return random.choice(self.behavior_patterns)

# GUI Application
class ShadowCrawlerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Advanced ShadowCrawler")
        self.root.geometry("800x600")
        self.root.configure(bg="#1e1e1e")
        
        self.log_queue = queue.Queue()
        self.create_widgets()
        self.crawler = None
        self.crawler_thread = None
        
        # Start queue processing
        self.process_log_queue()
        
    def create_widgets(self):
        # Style configuration
        style = ttk.Style()
        style.configure("TButton", background="#2a2a2a", foreground="#00ff00", font=("Courier New", 10))
        style.configure("TLabel", background="#1e1e1e", foreground="#00ff00", font=("Courier New", 12))
        style.configure("TFrame", background="#1e1e1e")
        
        # Main frame
        main_frame = ttk.Frame(self.root, style="TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="Advanced ShadowCrawler", font=("Courier New", 16, "bold"), foreground="#00ff00", background="#1e1e1e")
        title_label.pack(pady=10)
        
        # Control frame
        control_frame = ttk.Frame(main_frame, style="TFrame")
        control_frame.pack(fill=tk.X, pady=10)
        
        # Start button
        self.start_button = ttk.Button(control_frame, text="Start Crawler", command=self.start_crawler)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        # Stop button
        self.stop_button = ttk.Button(control_frame, text="Stop Crawler", command=self.stop_crawler, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Clear logs button
        self.clear_button = ttk.Button(control_frame, text="Clear Logs", command=self.clear_logs)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # Status frame
        status_frame = ttk.Frame(main_frame, style="TFrame")
        status_frame.pack(fill=tk.X, pady=5)
        
        # Status label
        self.status_label = ttk.Label(status_frame, text="Status: Ready", style="TLabel")
        self.status_label.pack(side=tk.LEFT)
        
        # Log area
        log_frame = ttk.Frame(main_frame, style="TFrame")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Log text area with scrollbar
        self.log_text = scrolledtext.ScrolledText(log_frame, bg="#000000", fg="#00ff00", font=("Courier New", 10))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)
        
        # Add some initial text
        self.add_log("[+] Advanced ShadowCrawler GUI initialized")
        self.add_log("[+] Ready to start operations")
        
    def add_log(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
    def process_log_queue(self):
        try:
            while not self.log_queue.empty():
                message = self.log_queue.get(0)
                self.add_log(message)
        except queue.Empty:
            pass
        finally:
            # Schedule to run again
            self.root.after(100, self.process_log_queue)
            
    def start_crawler(self):
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="Status: Running")
        
        # Create and start crawler in a separate thread
        self.crawler = ShadowCrawler(log_queue=self.log_queue)
        self.crawler_thread = threading.Thread(target=self.run_crawler)
        self.crawler_thread.daemon = True
        self.crawler_thread.start()
        
    def run_crawler(self):
        try:
            self.crawler.run(max_commands=10)
        except Exception as e:
            self.log_queue.put(f"[!] Error in crawler: {e}")
        finally:
            # Update UI when done
            self.root.after(0, self.crawler_finished)
            
    def crawler_finished(self):
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="Status: Ready")
        
    def stop_crawler(self):
        if self.crawler:
            self.log_queue.put("[!] Stopping crawler...")
            self.crawler.running = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.status_label.config(text="Status: Stopped")
            
    def clear_logs(self):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.add_log("[+] Logs cleared")

# Entry Point
if __name__ == "__main__":
    root = tk.Tk()
    app = ShadowCrawlerGUI(root)
    root.mainloop()
